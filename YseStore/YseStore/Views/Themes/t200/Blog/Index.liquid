{% assign visualPageData = ViewData["VisualPageData"] | jsonparse %}
<!--Page Header-->
<div class="page-header">
    <div class="page-title"><h1>{{ "blog.global.blog" | translate }}</h1></div>
</div>
<!--End Page Header-->


<!-- 分类调试信息 -->
{% comment %}<div class="container mt-3">{% endcomment %}
{% comment %}<div class="debug-info" style="background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; margin-bottom: 15px;">{% endcomment %}
{% comment %}<h6 style="color: #495057; margin-bottom: 8px;">🔍 分类数据调试</h6>{% endcomment %}
{% comment %}<small>{% endcomment %}
{% comment %}分类总数: <span style="color: #17a2b8;">{{ Model.Categories | size }}</span> |{% endcomment %}
{% comment %}当前分类ID: <span style="color: #007bff;">"{{ Model.CateId }}"</span>{% endcomment %}
{% comment %}{% if Model.Categories and Model.Categories.size > 0 %}{% endcomment %}
{% comment %}<br>分类列表:{% endcomment %}
{% comment %}{% for category in Model.Categories %}{% endcomment %}
{% comment %}<span style="color: #28a745;">"{{ category.Category }}" (ID: {{ category.CateId }})</span>{% unless forloop.last %}, {% endunless %}{% endcomment %}
{% comment %}{% endfor %}{% endcomment %}
{% comment %}{% endif %}{% endcomment %}
{% comment %}</small>{% endcomment %}
{% comment %}</div>{% endcomment %}
{% comment %}</div>{% endcomment %}



 {% for plugin in visualPageData.Plugins %}
    {% assign pluginType = plugin.Type | strip %}
      {% if pluginType == "blog_list" %}
      {% assign HotBlog =  plugin.Settings.HotBlog | strip  %}
      {% assign Month =  plugin.Settings.Month | strip  %}
      {% assign Category =  plugin.Settings.Category | strip  %}
      {% assign BriefIntroduction =  plugin.Settings.BriefIntroduction | strip %}

      {% assign isRight = HotBlog == "1" or Month == "1" or Category == "1" %}



<style>
    .ly_blog_list_1 {
        padding-top: {{ Model.settingsDynamic.UpperSpacing }}px!important;
        padding-bottom: {{ Model.settingsDynamic.LowerSpacing }}px!important;
    }
    .ly_blog_list_1 .blog--list-view {
        grid-template-columns: repeat({{ Model.settingsDynamic.ColumnsPc }}, calc((100% - {{ Model.settingsDynamic.ColumnsPc | minus: 1 | times: 30 }}px) / {{ Model.settingsDynamic.ColumnsPc }}))!important;
    }
    .ly_blog_list_1 .blog--list-view .article .item_text .btn-secondary {
        border-color: {{ Model.settingsDynamic.ButtonBorderColor }};
        color: {{ Model.settingsDynamic.ButtonTextColor }};
        background-color: {{ Model.settingsDynamic.ButtonBgColor }};
    }
    .ly_blog_list_1 .blog--list-view .article .item_text .btn-secondary:hover {
        border-color: {{ Model.settingsDynamic.ButtonBorderHoverColor }};
        color: {{ Model.settingsDynamic.ButtonHoverTextColor }};
        background-color: {{ Model.settingsDynamic.ButtonBgHoverColor }};
    }
    @media screen and (max-width: 1000px) {
        .ly_blog_list_1 {
            padding-top: {{ Model.settingsDynamic.UpperSpacingMobile }}px;
            padding-bottom: {{ Model.settingsDynamic.LowerSpacingMobile }}px;
        }
        .ly_blog_list_1 .blog--list-view {
            grid-template-columns: repeat({{ Model.settingsDynamic.ColumnsMobile }}, calc((100% - {{ Model.settingsDynamic.ColumnsMobile | minus: 1 | times: 15 }}px) / {{ Model.settingsDynamic.ColumnsMobile }}));
        }
    }
</style>



<div class="container pb-5 ly_blog_list_1">
    <div class="row flex-column-reverse flex-md-row {{Model.ContainWidth}}">
        <!--Main Content-->
        <div class="col-12 col-sm-12 col-md-9 col-lg-9 main-col {% unless isRight %}full_width{% endunless %}">
            <div class="blog--list-view blog--grid-load-more columns_pc_{{ plugin.Settings.ColumnsPc }} columns_mobile_{{ plugin.Settings.ColumnsMobile }} ">
                {% for blog in Model.Blogs %}
                    <div class="article">
                        <!-- Article Image -->
                        {% if blog.PicPath %}
                            <a class="article_featured-image" href="/blog/{{ blog.PageUrl }}" hx-target="#page-content"
                               hx-push-url="true" hx-swap="innerHTML">
                                <img class="blur-up lazyload blog-image" data-src="{{ blog.PicPath }}" src="{{ blog.PicPath }}"
                                     alt="{{ blog.Title }}">
                            </a>
                        {% endif %}
                        <div class="item_text">
                        <h2 class="h3"><a href="/blog/{{ blog.PageUrl }}" hx-target="#page-content" hx-push-url="true"
                                          hx-swap="innerHTML">{{ blog.Title }}</a></h2>
                        <ul class="publish-detail">
                            <li><i class="an an-user" aria-hidden="true"></i> {{ blog.Author }}</li>
                            <li><i class="icon an an-calendar-check"></i>
                                <span>{{ blog.AccTime | date: "%B %d, %Y" }}</span></li>
                            <li><i class="icon an an-comments"></i> <a
                                        href="#"> {{ blog.Comments }} {{ "blog.global.comments" | translate }}</a></li>
                        </ul>
                       
                         {% if BriefIntroduction == "1" %}
                        <div class="rte">
                            <p>{{ blog.BriefDescription | raw }}</p>
                        </div>
                        {% endif %}
                        <p><a href="/blog/{{ blog.PageUrl }}" hx-target="#page-content" hx-push-url="true"
                              hx-swap="innerHTML"
                              class="btn btn-secondary btn--small theme-btn">{{plugin.Settings.ButtonText}}</a>
                        </p>
                        </div>
                    </div>
                {% endfor %}

                {% if Model.Blogs.Count == 0 %}
                    <div class="article">
                        <p>{{ "web.global.no_data" | translate }}</p>
                    </div>
                {% endif %}

                <!--Pagination Classic-->
                <hr class="clear">
                <div class="pagination">
                    <ul>
                        {% if Model.CurrentPage > 1 %}
                            <li class="prev"><a
                                        href="/blog?page={{ Model.CurrentPage | minus: 1 }}{% if Model.Keyword != "" %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId != "" %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId != "" %}&tagId={{ Model.TagId }}{% endif %}"><i
                                            class="an an-lg an-angle-left" aria-hidden="true"></i></a></li>
                        {% else %}
                            <li class="prev disabled"><a href="#"><i class="an an-lg an-angle-left"
                                                                     aria-hidden="true"></i></a></li>
                        {% endif %}

                        {% assign startPage = Model.CurrentPage | minus: 2 %}
                        {% if startPage < 1 %}{% assign startPage = 1 %}{% endif %}

                        {% assign endPage = startPage | plus: 4 %}
                        {% if endPage > Model.TotalPages %}{% assign endPage = Model.TotalPages %}{% endif %}

                        {% if startPage > 1 %}
                            <li>
                                <a href="/blog?page=1{% if Model.Keyword != "" %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId != "" %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId != "" %}&tagId={{ Model.TagId }}{% endif %}">1</a>
                            </li>
                            {% if startPage > 2 %}
                                <li class="disabled"><a href="#">...</a></li>{% endif %}
                        {% endif %}

                        {% for i in (startPage..endPage) %}
                            <li {% if i == Model.CurrentPage %}class="active"{% endif %}>
                                <a href="/blog?page={{ i }}{% if Model.Keyword != "" %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId != "" %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId != "" %}&tagId={{ Model.TagId }}{% endif %}">{{ i }}</a>
                            </li>
                        {% endfor %}

                        {% if endPage < Model.TotalPages %}
                            {% assign lastPageMinusOne = Model.TotalPages | minus: 1 %}
                            {% if endPage < lastPageMinusOne %}
                                <li class="disabled"><a href="#">...</a></li>{% endif %}
                            <li>
                                <a href="/blog?page={{ Model.TotalPages }}{% if Model.Keyword != "" %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId != "" %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId != "" %}&tagId={{ Model.TagId }}{% endif %}">{{ Model.TotalPages }}</a>
                            </li>
                        {% endif %}

                        {% if Model.CurrentPage < Model.TotalPages %}
                            <li class="next"><a
                                        href="/blog?page={{ Model.CurrentPage | plus: 1 }}{% if Model.Keyword != "" %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId != "" %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId != "" %}&tagId={{ Model.TagId }}{% endif %}"><i
                                            class="an an-lg an-angle-right" aria-hidden="true"></i></a></li>
                        {% else %}
                            <li class="next disabled"><a href="#"><i class="an an-lg an-angle-right"
                                                                     aria-hidden="true"></i></a></li>
                        {% endif %}
                    </ul>
                </div>
                <!--End Pagination Classic-->
            </div>
        </div>
        <!--End Main Content-->


          {% if isRight  %}
        {% assign blogsidebar = '/Themes/' | append: theme | append: '/Blog/BlogSidebar' %}
        {% include blogsidebar -%}
        {% endif %}

    </div>
</div>
<!--End Page Wrapper-->
  {% endif %}
{% endfor %}

<!-- 导入图片处理助手 -->
<script src="/businessJs/imageUrlHelper.js"></script>
<script>
// 页面加载完成后处理所有图片URL
document.addEventListener('DOMContentLoaded', function() {
    // 处理博客列表页面中的所有图片
    const blogImages = document.querySelectorAll('.blog-image');
    blogImages.forEach(function(img) {
        if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
            // 为博客图片使用中等尺寸
            img.src = ImageUrlHelper.getLargeUrl(img.src);
        }
        if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
            img.dataset.src = ImageUrlHelper.getLargeUrl(img.dataset.src);
        }
    });

    // 处理博客侧边栏中的最新博客图片
    const sidebarBlogImages = document.querySelectorAll('.mini-list-item img');
    sidebarBlogImages.forEach(function(img) {
        if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
            // 为侧边栏小图使用缩略图尺寸
            img.src = ImageUrlHelper.getThumbnailUrl(img.src);
        }
        if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
            img.dataset.src = ImageUrlHelper.getThumbnailUrl(img.dataset.src);
        }
    });
});
</script>