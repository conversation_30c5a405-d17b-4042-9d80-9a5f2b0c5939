<!--Home slider-->
{% section meta_keywords -%}
<meta name="keywords" content="{{ SiteName }}">
{% endsection -%}
{% section meta_description -%}
<meta name="description" content="{{ SiteName }}">
{% endsection -%}
{% section title -%}
{% assign hastitle = true %}
<title>{{ SiteName }}</title>
{% endsection -%}
<!-- 解析ViewData["VisualPageData"]中的header配置 -->
{% assign visualPageData = ViewData["VisualPageData"] | jsonparse %}

{% comment %} 动态生成插件容器 - 排除header和footer类型 {% endcomment %}

{% comment %}<div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc; font-family: monospace; font-size: 12px;">{% endcomment %}
{% comment %}<h4>Index插件配置调试信息:</h4>{% endcomment %}
{% comment %}<p><strong>VisualPageData解析成功:</strong> {% if visualPageData %}是{% else %}否{% endif %}</p>{% endcomment %}
{% comment %}<p><strong>Plugins列表存在:</strong> {% if visualPageData.Plugins %}是{% else %}否{% endif %}</p>{% endcomment %}
{% comment %}<p><strong>Plugins总数量:</strong> {{ visualPageData.Plugins.size }}</p>{% endcomment %}
{% comment %}<p><strong>将处理的插件数量:</strong>{% endcomment %}
{% comment %}{% assign processCount = 0 %}{% endcomment %}
{% comment %}{% for plugin in visualPageData.Plugins %}{% endcomment %}
{% comment %}{% assign debugCleanType = plugin.Type | strip | downcase %}{% endcomment %}
{% comment %}{% unless debugCleanType == "header" or debugCleanType == "footer" %}{% endcomment %}
{% comment %}{% assign processCount = processCount | plus: 1 %}{% endcomment %}
{% comment %}{% endunless %}{% endcomment %}
{% comment %}{% endfor %}{% endcomment %}
{% comment %}{{ processCount }}</p>{% endcomment %}
{% comment %}{% if visualPageData.Plugins.size > 0 %}{% endcomment %}
{% comment %}<p><strong>所有插件类型:</strong></p>{% endcomment %}
{% comment %}<ul>{% endcomment %}
{% comment %}{% for plugin in visualPageData.Plugins %}{% endcomment %}
{% comment %}{% assign debugCleanType = plugin.Type | strip | downcase %}{% endcomment %}
{% comment %}<li>PId: {{ plugin.PId }}, Type: "{{ plugin.Type }}" (清理后: "{{ debugCleanType }}"), Mode: "{{ plugin.Mode }}", Location: "{{ plugin.Location }}"</li>{% endcomment %}
{% comment %}{% endfor %}{% endcomment %}
{% comment %}</ul>{% endcomment %}
{% comment %}<p><strong>将要处理的插件:</strong></p>{% endcomment %}
{% comment %}<ul>{% endcomment %}
{% comment %}{% for plugin in visualPageData.Plugins %}{% endcomment %}
{% comment %}{% assign debugCleanType = plugin.Type | strip | downcase %}{% endcomment %}
{% comment %}{% unless debugCleanType == "header" or debugCleanType == "footer" %}{% endcomment %}
{% comment %}<li>✓ PId: {{ plugin.PId }}, Type: "{{ plugin.Type }}", Mode: "{{ plugin.Mode }}", Location: "{{ plugin.Location }}"</li>{% endcomment %}
{% comment %}{% else %}{% endcomment %}
{% comment %}<li>✗ 跳过: PId: {{ plugin.PId }}, Type: "{{ plugin.Type }}"</li>{% endcomment %}
{% comment %}{% endunless %}{% endcomment %}
{% comment %}{% endfor %}{% endcomment %}
{% comment %}</ul>{% endcomment %}
{% comment %}{% endif %}{% endcomment %}
{% comment %}</div>{% endcomment %}


{% comment %} 为每个插件生成visual_plugins_container - 跳过header和footer {% endcomment %}
{% for plugin in visualPageData.Plugins %}
    {% assign pluginType = plugin.Type | strip %}
    {% assign cleanType = pluginType | downcase %}

    {% comment %} 跳过header和footer类型的插件 {% endcomment %}
    {% unless cleanType == "header" or cleanType == "footer" %}
        {% assign pluginMode = plugin.Mode | default: "mode_1" %}
        {% assign pluginLocation = forloop.index %}

        <div class="visual_plugins_container" data-type="{{ pluginType }}" data-mode="{{ pluginMode }}"
             data-location="{{ pluginLocation }}">

            {% comment %} 根据插件类型渲染不同内容 {% endcomment %}
            {% if pluginType == "carousel" %}
                {% comment %} Carousel轮播图插件 {% endcomment %}
                <div class="slideshow slideshow-wrapper" data-visual-id="{{ plugin.PId }}">
                    <div class="home-slideshow">
                        {% comment %} 动态生成carousel slides {% endcomment %}
                        {% if plugin.Blocks %}
                            {% for block in plugin.Blocks %}
                                {% assign blockKey = block[0] %}
                                {% assign blockData = block[1] %}
                                <div class="slide">
                                    <div class="blur-up lazyload">
                                        {% comment %} PC端图片 {% endcomment %}
                                        {% if blockData.PicPc %}
                                            <img class="blur-up lazyload desktop-hide sw-slide"
                                                 data-src="{{ blockData.PicPc }}"
                                                 src="{{ blockData.PicPc }}"
                                                 alt="{% if blockData.ImageAltPc %}{{ blockData.ImageAltPc }}{% else %}{{ blockData.Title }}{% endif %}"
                                                 title="{% if blockData.ImageAltPc %}{{ blockData.ImageAltPc }}{% else %}{{ blockData.Title }}{% endif %}"/>
                                        {% endif %}

                                        {% comment %} 移动端图片 {% endcomment %}
                                        {% if blockData.PicMobile %}
                                            <img class="blur-up lazyload mobile-hide sw-slide"
                                                 data-src="{{ blockData.PicMobile }}"
                                                 src="{{ blockData.PicMobile }}"
                                                 alt="{% if blockData.ImageAltMobile %}{{ blockData.ImageAltMobile }}{% else %}{{ blockData.TitleMobile | default: blockData.Title }}{% endif %}"
                                                 title="{% if blockData.ImageAltMobile %}{{ blockData.ImageAltMobile }}{% else %}{{ blockData.TitleMobile | default: blockData.Title }}{% endif %}"/>
                                        {% elsif blockData.PicPc %}
                                            {% comment %} 如果没有移动端图片，使用PC端图片 {% endcomment %}
                                            <img class="blur-up lazyload mobile-hide sw-slide"
                                                 data-src="{{ blockData.PicPc }}"
                                                 src="{{ blockData.PicPc }}"
                                                 alt="{% if blockData.ImageAltPc %}{{ blockData.ImageAltPc }}{% else %}{{ blockData.Title }}{% endif %}"
                                                 title="{% if blockData.ImageAltPc %}{{ blockData.ImageAltPc }}{% else %}{{ blockData.Title }}{% endif %}"/>
                                        {% endif %}

                                        {% comment %} 文字内容覆盖层 {% endcomment %}
                                        {% if blockData.Title or blockData.Content %}
                                            <div class="slideshow__text-wrap slideshow__overlay {{ blockData.TextPosition | default: 'left' }}">
                                                <div class="slideshow__text-content">
                                                    <div class="slide-content"
                                                    >
                                                        <h2 class="slide-title">
                                                        <span {% if blockData.TitleClass != '' %}class="{{ blockData.TitleClass }}"{% endif %}
                                                              style="{% if blockData.TitleColor != '' %}color: {{ blockData.TitleColor }};
                                                              {% endif %}{% if blockData.TitleFontSize != '' %} font-size: {{ blockData.TitleFontSize }};
                                                              {% endif %}{% if blockData.TitleFontWeight != '' %} font-weight: {{ blockData.TitleFontWeight }};{% endif %}">
                                                            {{ blockData.Title }}
                                                        </span>
                                                            <br>
                                                            {{ blockData.TitleTwo | default: "" }}
                                                        </h2>

                                                        <p class="slide-desc" style="color: #414141; font-weight: 300;">
                                                            {{ blockData.TitleThree | default: "" }} <span
                                                                    style="color: #1f909e; font-weight: 500;">{{ blockData.TitleFour | default: "" }}</span>
                                                            {{ blockData.TitleFive | default: "" }}</p>
                                                        <a href="{{ blockData.Href | default: "" }}"
                                                           class="slide-button"> {{ blockData.ButtonText | default: "" }}</a>

                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>

            {% elsif pluginType == "poster" %}
                {% assign trimmedMode = pluginMode | strip %}
                {% if trimmedMode == "mode_2" %}
                    {% comment %} Mode为mode_2时展示features特色插件样式 {% endcomment %}
                    <div class="store-features section" data-visual-id="{{ plugin.PId }}">
                        <div class="container">
                            <div class="row">
                                {% if plugin.Blocks %}
                                    {% for block in plugin.Blocks %}
                                        {% assign blockKey = block[0] %}
                                        {% assign blockData = block[1] %}
                                        <div class="col-12 col-sm-6 col-md-3 col-lg-3 home-policy">
                                            {% comment %} 使用Icon字段显示图标 {% endcomment %}
                                            {% if blockData.Icon %}
                                                <i class="{{ blockData.Icon }}"></i>
                                            {% endif %}

                                            {% if blockData.Title %}
                                                <h5>{{ blockData.Title }}</h5>
                                            {% endif %}
                                            {% if blockData.TitleTwo %}
                                                <p class="sub-text">{{ blockData.TitleTwo }}</p>
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div style="background: red; color: white; padding: 10px;">
                                        plugin.Blocks 为空或不存在
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% elsif trimmedMode == "mode_3" %}
                    {% comment %} Mode为mode_3时展示新产品展示样式 {% endcomment %}
                    <div class="pb-4" style="padding-top: 10px;">
                        <div class="container"
                             style="max-width: 1800px; background-image: linear-gradient(to top, #8f9095de, #fff, #b3b5bfb3);">
                            <div class="row">
                                <div class="col-12 col-sm-12 col-md-3 col-lg-3"
                                     style="display: flex; justify-content: center;">
                                    {% if plugin.Blocks.Title %}
                                        {% assign titleData = plugin.Blocks.Title %}
                                        <div style="padding: 14% 14% 8%;" class="d-lg-block d-none">
                                            <h1 class="new-arrival-h1"
                                                style="font-size: 45px; font-weight: bold; margin-top: -30px;">
                                                {{ titleData.Title | default: "Nuevo producto" }}
                                            </h1>
                                            <p class="new-arrival-p"
                                               style="color: #3d4482; font-size: 17px; font-weight: bold;">
                                                {{ titleData.TitleSub | default: "Los nuevos productos están a la venta a un precio especial, ¡cómpralo ahora!" }}
                                            </p>
                                            <a href="{{ titleData.Href | default: '/collections' }}">
                                                {% if titleData.Pic %}
                                                    <img class="new-arrival-img" style="width: 65%; margin-top: -10px;"
                                                         src="{{ titleData.Pic }}">
                                                {% else %}
                                                    <img class="new-arrival-img" style="width: 65%; margin-top: -10px;"
                                                         src="{{ static_path }}/assets/images/index/new-arriva.png">
                                                {% endif %}
                                            </a>
                                        </div>
                                        <div class="d-lg-none d-block">
                                            <div style="padding: 20px; margin-top: 20px;">
                                                <h1 style="font-size: 28px; font-weight: bold; margin-top: -15px;">{{ titleData.Title | default: "Nuevo producto" }}</h1>
                                                <p style="color: #3d4482; font-size: 20px; font-weight: bold;">{{ titleData.TitleSub | default: "¡Compra ahora y deja que te traigan una nueva experiencia!" }}</p>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="productSlider-style2 col-12 col-sm-12 col-md-9 col-lg-9"
                                     style="margin-top: 3%;">
                                    {% if plugin.Blocks %}
                                        {% for block in plugin.Blocks %}
                                            {% assign blockKey = block[0] %}
                                            {% assign blockData = block[1] %}
                                            {% if blockKey contains "Poster-" %}
                                                <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                                                    <a href="{{ blockData.Href | default: '#' }}" tabindex="0">
                                                        <div class="grid-view_image"
                                                             style="position: relative; overflow: hidden; border-radius: 10px;">
                                                            <img style="border-radius: 10px; width: 100%; transition: transform 0.3s ease;"
                                                                 src="{{ blockData.Pic }}"
                                                                 alt="{{ blockData.ImageAlt | default: blockData.Title }}">
                                                            <div class="hover-effect"
                                                                 style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(31, 144, 158, 0); transition: all 0.3s ease;">
                                                                <button class="shop-btn"
                                                                        style="background: #1f909e; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-weight: 600; font-size: 16px; cursor: pointer; opacity: 0; transform: translateY(20px); transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);"
                                                                        tabindex="0"> Shop Now
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% elsif trimmedMode == "mode_234" %}
                    <div style="background-color: #f4f7fc; ">
                        <div class="container" style="max-width: 1800px;">
                            <!--联系我们模块-->
                            <div class="about-us d-lg-block d-none" style="padding: 30px 0px;">
                                <div style="padding: 0 5%;">
                                    <div class="row">
                                        <div class="col-12 col-sm-12 col-md-4 col-lg-4"
                                             style="display: flex; align-items: center; flex-wrap: wrap; border-right: 1px solid #e5e5e5; flex-direction: column; margin-top: 30px;">
                                            <p style="font-size: 20px; font-weight: bold; margin-bottom: 20px; width: 100%;">{{ plugin.Settings.Title }}</p>
                                            <p style="font-size: 20px; font-weight: bold; width: 100%;">{{ plugin.Settings.Content }}</p>
                                        </div>
                                         {% assign cleanBlocks = plugin.Blocks %}
                                         {% for block in cleanBlocks %}
                                         {% assign poster = block[1] %}
                                                <div class="col-12 col-sm-12 col-md-4 col-lg-4" style="{% unless forloop.last %}border-right: 1px solid #e5e5e5;{% endunless %}">
                                                    <img src="{{ poster.Pic }}"
                                                         style="margin-bottom: 5px;">
                                                    <p style="font-weight: bold;"><a class="emaillink" href="{{ poster.Link[1] }}">{{ poster.Title }}</a></p>
                                                    <p>{{ poster.ImageAlt }}</p>
                                                </div>
                                         {% endfor %}

                                    
                                    </div>
                                </div>
                            </div>
                        
                            <!--联系我们模块-->
                        </div>
                    </div>

                {% elsif trimmedMode == "mode_233" %}
                    {% comment %} 其他Mode的poster展示方式 {% endcomment %}
                    <div class="container section" style="max-width: 1800px;">
                        <div class="section-header">
                            <h2 class="section-header__title text-center mb-2"><span>{{ plugin.Settings.Title }}</span>
                            </h2>
                        </div>
                        <div class="grid-products wireless-solutions">
                            <div class="productSlider-style2">
                                {% assign cleanBlocks = plugin.Blocks %}
                                {% for block in cleanBlocks %}
                                    {% assign poster = block[1] %}
                                    <div class="item">
                                        <div class="grid-view_image">
                                            <a href="{{ poster.Link[1] }}" tabindex="0">
                                                <div class="btn07">
                                                    <img src="{{ poster.Pic }}"
                                                         style="border-radius: 20px;">
                                                    <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                                        <h2 class="wireless-solutions-h2">{{ poster.Title }}</h2>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% elsif trimmedMode == "mode_236" %}
                <div class="container section" style="max-width: 1800px;background-color: #fff;">
                    <div class="section-header">
                        <h2 class="section-header__title text-center mb-2"><span>{{ plugin.Settings.Title }}</span></h2>
                    </div>
                    <div class=" grid-products">
                        <div class="productSlider-style2">
                         {% assign cleanBlocks = plugin.Blocks %}
                                {% for block in cleanBlocks %}
                                    {% assign poster = block[1] %}
                            <div class="item">
                                <div class="grid-view_image">
                                <img src="{{ poster.Pic }}"style="border-radius: 10px;"></div>
                            </div>
                             {% endfor %}
                           
                        </div>
                    </div>
                </div>
                {% elsif trimmedMode == "mode_235" %}
                <div class="content-bg mb-4 py-3">
                    <div class="container" style="max-width: 1800px;">
                        <h2 class="d-lg-block d-none" style="font-size: 36px; font-weight: bold; text-align: center;">{{ plugin.Settings.Title }}</h2>
                        <div class="row">
                         {% assign cleanBlocks = plugin.Blocks %}
                                {% for block in cleanBlocks %}
                                    {% assign poster = block[1] %}
                            <div style="background-color: #fff; padding: 10px;" class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <div style="border: 1px solid #dee2e6; padding: 20px;">
                                    <a href="#"><img src="{{ poster.Pic }}"></a>
                                    <p style="font-weight: bold; margin-top: 20px;"><a href="{{ poster.Link[1] }}">{{ poster.Title }}</a></p>
                                    <p>{{ poster.ImageAlt }}</p>
                                </div>
                            </div>
                             {% endfor %}
                       

                        </div>
                    </div>
                </div>
                {% endif %}

            {% elsif pluginType == "products" %}
                {% comment %} Products产品插件 - 动态渲染产品区块 {% endcomment %}
                {% comment %} 检查是否是第一个products插件，如果是则开始容器 {% endcomment %}
                {% assign isFirstProductsPlugin = true %}
                {% for prevPlugin in visualPageData.Plugins %}
                    {% assign prevPluginType = prevPlugin.Type | strip %}
                    {% assign prevCleanType = prevPluginType | downcase %}
                    {% unless prevCleanType == "header" or prevCleanType == "footer" %}
                        {% if prevPluginType == "products" and forloop.index < pluginLocation %}
                            {% assign isFirstProductsPlugin = false %}
                            {% break %}
                        {% endif %}
                    {% endunless %}
                {% endfor %}

                {% comment %} 检查是否是最后一个products插件，如果是则结束容器 {% endcomment %}
                {% assign isLastProductsPlugin = true %}
                {% for nextPlugin in visualPageData.Plugins %}
                    {% assign nextPluginType = nextPlugin.Type | strip %}
                    {% assign nextCleanType = nextPluginType | downcase %}
                    {% unless nextCleanType == "header" or nextCleanType == "footer" %}
                        {% if nextPluginType == "products" and forloop.index > pluginLocation %}
                            {% assign isLastProductsPlugin = false %}
                            {% break %}
                        {% endif %}
                    {% endunless %}
                {% endfor %}

                {% if isFirstProductsPlugin %}
                    <div style="background-color: #f1f3f5;">
                        <div class="container section" style="max-width: 1800px;">
                {% endif %}

                {% comment %} 查找对应的VisualProductBlock数据 {% endcomment %}
                {% assign currentProductBlock = null %}
                {% assign pluginPIdStr = plugin.PId | append: "" %}
                {% for productBlock in Model.VisualProductBlocks %}
                    {% assign productBlockPIdStr = productBlock.PId | append: "" %}
                    {% if productBlockPIdStr == pluginPIdStr %}
                        {% assign currentProductBlock = productBlock %}
                        {% break %}
                    {% endif %}
                {% endfor %}

                <div data-visual-id="{{ plugin.PId }}">
                    {% comment %} 调试信息 {% endcomment %}
                    <div style="background: red; color: white; padding: 5px; margin: 5px;">
                        调试: PId={{ plugin.PId }},
                        有数据: {% if currentProductBlock %}是{% else %}否{% endif %},
                        产品数量: {% if currentProductBlock.Products %}{{ currentProductBlock.Products.size }}{% else %}0{% endif %},
                        Mode: "{{ currentProductBlock.Mode }}"
                    </div>

                    {% if currentProductBlock and currentProductBlock.Products and currentProductBlock.Products.size > 0 %}
                        {% comment %} 获取trimmedMode和mode_4变量 {% endcomment %}
                        {% assign trimmedMode = currentProductBlock.Mode | strip %}
                        {% assign mode_4 = "mode_4" %}
                        {% comment %} 添加条件判断：如果trimmedMode等于mode_4 {% endcomment %}
                        {% if trimmedMode == mode_4 %}
                            <div class="related-product grid-products">
                                <div class="section-header">
                                    {% if currentProductBlock.Title and currentProductBlock.Title != "" %}
                                        <h2 class="section-header__title text-center mb-2">
                                            <span>{{ currentProductBlock.Title }}</span></h2>
                                    {% endif %}
                                    <div class="row mt-4 mb-2">
                                        <div class="col-8 text-left">
                                            {% if currentProductBlock.SubTitle and currentProductBlock.SubTitle != "" %}
                                                <h3 style="font-size: 26px; position: relative;">
                                                    {% if currentProductBlock.Link and currentProductBlock.Link != "" %}
                                                        <a href="{{ currentProductBlock.Link }}">{{ currentProductBlock.SubTitle }}</a>
                                                    {% else %}
                                                        {{ currentProductBlock.SubTitle }}
                                                    {% endif %}
                                                </h3>
                                            {% endif %}
                                        </div>
                                        <div class="col-4 text-right">
                                            {% if currentProductBlock.LinkName and currentProductBlock.LinkName != "" and currentProductBlock.Link and currentProductBlock.Link != "" %}
                                                <div><a style="font-weight: bold; color: #000;"
                                                        href="{{ currentProductBlock.Link }}">{{ currentProductBlock.LinkName }}
                                                        &gt;</a></div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <!-- 动态循环产品 -->
                                <div class="row">
                                    {% for product in currentProductBlock.Products %}
                                        <div class="col-6 col-sm-6 col-md-4 col-lg-2 item">
                                            <div class="p-4 bg-white">
                                                <!-- start product image -->
                                                <div class="product-image">
                                                    <!-- start product image -->
                                                    <a href="{{ product.ProductUrl }}" class="product-img">
                                                        <!-- image -->
                                                        <img class="primary blur-up lazyload"
                                                             data-src="{% if product.PrimaryImage %}{{ product.PrimaryImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227{% else %}{{ static_path }}/assets/images/product-images/elt-p-10.jpg{% endif %}"
                                                             src="{% if product.PrimaryImage %}{{ product.PrimaryImage }}{% else %}{{ static_path }}/assets/images/product-images/elt-p-10.jpg{% endif %}"
                                                             alt="{{ product.ProductName }}"
                                                             title="{{ product.ProductName }}">
                                                        <!-- End image -->
                                                        <!-- Hover image -->
                                                        {% if product.HoverImage %}
                                                            <img class="hover blur-up lazyload"
                                                                 data-src="{{ product.HoverImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227"
                                                                 src="{{ product.HoverImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227"
                                                                 alt="{{ product.ProductName }}"
                                                                 title="{{ product.ProductName }}">
                                                        {% endif %}
                                                        <!-- End hover image -->
                                                    </a>
                                                    <!-- end product image -->
                                                    <!--Product label-->
                                                    {% if product.IsHot or product.IsNew %}
                                                        <div class="product-labels">
                                                            {% if product.IsHot %}<span
                                                                    class="lbl pr-label2">Hot</span>{% endif %}
                                                            {% if product.IsNew %}<span
                                                                    class="lbl pr-label1">New</span>{% endif %}
                                                        </div>
                                                    {% endif %}
                                                    <!--Product label-->
                                                    <!--Product Button-->
                                                    <div class="button-set style1">
                                                        <ul>
                                                            <li>
                                                                <!--Cart Button-->
                                                                <a href="{{ product.ProductUrl }}"
                                                                   title="{{ "products.goods.addToCart" | translate }}"
                                                                   class="btn-icon btn-addto-cart"
                                                                   data-product-id="{{ product.ProductId }}">
                                                                    <i class="icon an an-shopping-cart"></i>
                                                                    <span class="tooltip-label">{{ "products.goods.addToCart" | translate }}</span>
                                                                </a>
                                                                <!--End Cart Button-->
                                                            </li>
                                                            <li>
                                                                <!--Wishlist Button-->
                                                                <div class="wishlist-btn">
                                                                    <a class="btn-icon wishlist add-to-wishlist"
                                                                       href="javascript:void(0);"
                                                                       data-product-id="{{ product.ProductId }}"
                                                                       data-is-favorited="{% if product.IsFavorited %}true{% else %}false{% endif %}">
                                                                        {% if product.IsFavorited %}
                                                                            <i class="icon an an-heart"></i>
                                                                        {% else %}
                                                                            <i class="icon an an-heart-o"></i>
                                                                        {% endif %}
                                                                        <span class="tooltip-label">{{ "products.goods.addToFavorites" | translate }}</span>
                                                                    </a>
                                                                </div>
                                                                <!--End Wishlist Button-->
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <!--End Product Button-->
                                                </div>
                                                <!-- end product image -->
                                                <!--start product details -->
                                                <div class="product-details text-left">
                                                    <!-- product name -->
                                                    <div class="product-name">
                                                        <a href="{{ product.ProductUrl }}">{{ product.ProductName }}</a>
                                                    </div>
                                                    <!-- End product name -->
                                                    <!-- product price -->
                                                    <div class="product-price">
                                                        <span class="price">{{ product.PriceFormat }}</span>
                                                        {% if product.OldPriceFormat and product.OldPriceFormat != "" %}
                                                            <span class="old-price">{{ product.OldPriceFormat }}</span>
                                                        {% endif %}
                                                    </div>
                                                    <!-- End product price -->
                                                </div>
                                                <!-- End product details -->
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}
                </div>

                {% if isLastProductsPlugin %}
                        </div>
                    </div>
                {% endif %}

            {% elsif pluginType == "blog" %}
                {% comment %} Blog博客插件 - 暂时显示占位内容 {% endcomment %}
                <div class="container section" style="max-width: 1800px;background-color: #fff;">
                    <div class="section-header">
                        <h2 class="section-header__title text-center mb-2"><span>Blog</span></h2>
                    </div>
                    <div class="collection-slider latest-blog">
                        <div class="collection-slider-4items">
                            {% for blog in Model.blogList %}
                                <div class="collection-grid-item">
                                    <div class="wrap-blog">
                                        <a href="/blog/{{ blog.PageUrl }}" class="article__grid-image" tabindex="0">
                                            <img src="{{ blog.PicPath }}"
                                                 alt="{{ blog.Title }}"
                                                 title="{{ blog.Title }}" class="blur-up lazyloaded">
                                        </a>
                                        <div class="article__grid-meta article__grid-meta--has-image">
                                            <div class="wrap-blog-inner">
                                                <h2 class="h3 article__title text-capitalize">
                                                    <a href="/blog/{{ blog.PageUrl }}" tabindex="0">{{ blog.Title }}</a>
                                                </h2>
                                                <span class="article__date">{{ blog.AccTime | date: "%B %d, %Y" }}</span>
                                                <div class="article__grid-excerpt">
                                                    {{ blog.BriefDescription | raw }}
                                                </div>
                                                <ul class="list--inline article__meta-buttons">
                                                    <li><a href="/blog/{{ blog.PageUrl }}" class="text-capitalize"
                                                           tabindex="0">{{ "blog.global.readMore" | translate }}</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                
            {% else %}
                {% comment %} 其他未知插件类型 {% endcomment %}
                <div class="unknown-plugin-container">
                    <p>未知插件类型: {{ pluginType }} - PId: {{ plugin.PId }}, Mode: {{ pluginMode }}</p>
                </div>
            {% endif %}

        </div>
    {% endunless %}
{% endfor %}



<!--End Home slider-->



<!--<div class="container section" style="max-width: 1800px;background-color: #fff;">
    <div class="section-header">
        <h2 class="section-header__title text-center mb-2"><span>Testimonials from Our Partners</span></h2>
    </div>
    <div class="blog-post-slider">
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">- Rob Stinson</p>
        </div>
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">-Jason Murray</p>
        </div>
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">-Terry Thompson</p>
        </div>
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">-David B.</p>
        </div>
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">-Steven Vesely</p>
        </div>
    </div>
</div>-->

<style>
    .wireless-solutions .wireless-solutions-h2 {
        color: black;
        font-size: 22px;
        font-weight: bold;
        margin-bottom: 0;
    }

    .home-slideshow .slide-content {
        /*        position: absolute;
        top: 22%;
        bottom: 22%;
        left: 8.6%;*/
        width: 670px;
        color: #fff;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        transition: all 0.3s ease;
        text-align: left;
    }

    .slide-title {
        color: #000;
        font-size: clamp(32px, 4vw, 56px);
        font-weight: 700;
        line-height: 1.2;
        margin: 0;
        padding-top: 20px;
    }

    .slide-desc {
        font-size: clamp(16px, 1.8vw, 26px);
        line-height: 1.4;
        margin: 0 0 2em 0;
    }

    .slide-button {
        display: inline-block;
        padding: 12px 40px;
        background: #FF6B00;
        color: #fff;
        font-size: clamp(14px, 1.5vw, 20px);
        font-weight: 700;
        border-radius: 30px;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin-bottom: 20px;
        width: fit-content;
    }

    .section-header h2 {
        font-size: 34px;
        font-weight: 700;
        line-height: 1.3;
        text-transform: capitalize;
        margin: 0 auto;
    }

    .grid-view_image {
        position: relative;
        overflow: hidden;
        margin: 0 auto 15px;
    }

    .content-bg {
        background: #fafafa;
    }

    .about-us-num-pc {
        position: absolute;
        top: 30%;
        left: 15%;
        text-align: center;
        width: 70%;
        display: flex;
    }

    .new-h2 {
        font-size: 40px;
        margin-bottom: 1.5rem;
    }

    .new-p1 {
        font-weight: bold;
        font-size: 20px;
    }

    .new-p2 {
        font-size: 18px;
    }

    .aboutus-con-pc {
        position: absolute;
        width: 56%;
        left: 22%;
        top: 70%;
        text-align: center;
        font-size: 18px;
    }

    .about-us-num-m {
        position: absolute;
        top: 8%;
        left: 30%;
        text-align: center;
        width: 40%;
        display: flex;
        flex-direction: column;
    }

    .aboutus-con-m {
        position: absolute;
        width: 90%;
        left: 5%;
        top: 75%;
        text-align: center;
        font-size: 14px;
    }

    .new-p2-m {
        font-size: 12px;
    }

    .tab-slider-product .tabs > li a {
        background-color: #ddd;
        font-size: 20px;
    }

    .home-brick-box .brick-title {
        font-size: 36px;
        text-align: center;
        margin: 0;
        padding-bottom: 15px;
        font-weight: bold;
        line-height: 1.5;
    }

    .post-excerpt.text-left.more {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
    }

    @media screen and (max-width: 989px) {
        .new-h1-m {
            font-size: 40px;
        }

        .new-h2-m {
            font-size: 24px;
        }

        .new-p2-m {
            font-size: 20px;
        }

        .about-us-num-m {
            top: 15%;
        }

        .aboutus-con-m {
            top: 70%;
            font-size: 20px;
        }

        .slideshow__text-wrap .anim-tru.style1 {
            text-align: center;
            width: 100%;
        }
    }

    @media screen and (min-width: 340px) and (max-width: 576px) {
        .new-h1-m {
            font-size: 20px;
        }

        .about-us-num-m {
            top: 6%;
        }

        .aboutus-con-m {
            font-size: 12px;
        }
    }

    @media screen and (min-width: 200px) and (max-width: 340px) {
        .new-h1-m {
            font-size: 20px;
        }

        .about-us-num-m {
            top: 6%;
        }

        .aboutus-con-m {
            font-size: 12px;
        }
    }
</style>
<style>
    .sw-nav-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 50px;
        height: 50px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 3;
        transition: all 0.3s ease;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .sw-nav-button:hover {
        background-color: rgba(255, 255, 255, 0.4);
    }

    .sw-nav-button.prev {
        left: 20px;
    }

    .sw-nav-button.next {
        right: 20px;
    }

    .sw-nav-button i {
        color: white;
        font-size: 24px;
    }

    .sw-container {
        --active-color: #fff;
        --inactive-color: #f5f5f569;
        position: relative;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        height: auto;
        aspect-ratio: 16/9;
        overflow: hidden;
        transition: height 0.3s ease;
    }

    .sw-slides {
        position: relative;
        width: 100%;
        height: 100%;
    }

    /*    .sw-slide {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

        .sw-slide.active {
            opacity: 1;
            z-index: 1;
        }

        .sw-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center center;
            display: block;
            backface-visibility: hidden;
        }*/

    .slide-content {
        /*        position: absolute;
        top: 22%;
        bottom: 22%;
        left: 8.6%;
        width: 670px;
        color: #fff;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        transition: all 0.3s ease;
        text-align:left;*/
    }

    .slide-title {
        color: #000;
        font-size: clamp(32px, 4vw, 56px);
        font-weight: 700;
        line-height: 1.2;
        margin: 0;
        padding-top: 20px;
        /*        opacity: 0;*/
        transform: translateY(30px);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .slide-desc {
        font-size: clamp(16px, 1.8vw, 26px);
        line-height: 1.4;
        margin: 0 0 2em 0;
        /*        opacity: 0;*/
        transform: translateY(30px);
        transition: all 0.6s 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .slide-button {
        display: inline-block;
        padding: 12px 40px;
        background: #FF6B00;
        color: #fff;
        font-size: clamp(14px, 1.5vw, 20px);
        font-weight: 700;
        border-radius: 30px;
        text-decoration: none;
        /*        opacity: 0;*/
        transform: translateY(30px);
        transition: all 0.6s 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin-bottom: 20px;
        width: fit-content;
    }

    /*    .sw-slide.active .slide-title,
    .sw-slide.active .slide-desc,
    .sw-slide.active .slide-button {
        opacity: 1;
        transform: translateY(0);
    }*/

    .sw-indicators {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
        display: flex;
        gap: 10px;
        padding: 5px;
        border-radius: 8px;
    }

    .sw-indicator {
        width: 40px;
        height: 3px;
        background: var(--inactive-color);
        cursor: pointer;
        transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: left center;
    }

    .sw-indicator.active {
        background: var(--active-color);
        transform: scaleX(1);
    }

    /* 周年庆banner样式 */
    .anni-bg-img {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 0;
    }

    .anni-particles-bg {
        position: relative;
        height: 100%;
        width: 100%;
        z-index: 1;
    }

    .anni-particles-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        /*background-color: rgba(0, 0, 0, 0.3);*/
        z-index: -1;
    }

    .anni-particle {
        position: absolute;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.5);
        animation: anni-float 5s infinite ease-in-out;
        z-index: 1;
    }

    @keyframes anni-float {

        0%, 100% {
            transform: translateY(0) translateX(0);
            opacity: 0.8;
        }

        50% {
            transform: translateY(-20px) translateX(10px);
            opacity: 0.3;
        }
    }

    .anni-content {
        position: relative;
        z-index: 2;
        width: 100%;
        padding: 0 20px;
        text-align: center;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .anni-main-title {
        font-size: clamp(2rem, 4vw, 4rem);
        font-weight: 800;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
        margin-bottom: 1rem;
        letter-spacing: -1px;
        line-height: 1.2;
        color: white;
    }

    .anni-sub-title {
        font-size: clamp(1rem, 1.5vw, 1.5rem);
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        margin-bottom: 2rem;
        max-width: 500px;
    }

    .anni-countdown-container {
        margin: 2rem 0;
    }

    .anni-countdown-title {
        font-size: clamp(14px, 1.5vw, 25px);
        margin-bottom: 1rem;
        color: rgba(255, 255, 255, 0.85);
    }

    .anni-countdown-box {
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .anni-countdown-item {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.15);
        border-radius: 10px;
        padding: clamp(15px, 2vw, 25px) clamp(15px, 2vw, 35px);
        min-width: 60px;
        text-align: center;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .anni-countdown-value {
        font-size: clamp(1.5rem, 3vw, 2.2rem);
        font-weight: 800;
        line-height: 1;
        letter-spacing: -1px;
    }

    .anni-countdown-label {
        font-size: clamp(0.7rem, 1.5vw, 0.85rem);
        opacity: 0.9;
    }

    .anni-cta-btn {
        background: linear-gradient(135deg, #FF6B00 0%, #e05a00 100%);
        color: white;
        border: none;
        padding: clamp(10px, 1.5vw, 14px) clamp(20px, 2.5vw, 36px);
        font-size: clamp(1rem, 1.5vw, 1.2rem);
        font-weight: 600;
        border-radius: 50px;
        box-shadow: 0 4px 15px rgba(255, 107, 0, 0.4);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        margin-top: 10px;
        text-decoration: none;
        display: inline-block;
    }

    .anni-cta-btn:hover {
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(255, 107, 0, 0.6);
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .sw-container {
            aspect-ratio: 3/4;
            max-height: 100vh;
        }

        /*        .sw-slide,
        .sw-slide img,
        .anni-particles-bg {
            height: 100% !important;
        }*/
        /* 周年庆内容区域调整 */
        .anni-content {
            padding: 0% 15px 0%;
            justify-content: flex-start;
            transform: none;
            left: 0;
            top: 0;
            width: 100%;
            box-sizing: border-box;
        }

        .anni-main-title {
            font-size: 1.5rem !important;
            line-height: 1.3;
            margin-bottom: 0.8rem;
            padding: 0 10px;
        }

        .anni-countdown-container {
            margin: 0rem auto;
            width: 100%;
            max-width: 220px;
        }

        .anni-countdown-box {
            gap: 5px;
        }

        .anni-countdown-item {
            padding: 8px 5px !important;
            min-width: 40px;
            flex-grow: 1;
        }

        .anni-countdown-value {
            font-size: 1.1rem;
        }

        .anni-countdown-label {
            font-size: 0.6rem;
        }

        .anni-cta-btn {
            padding: 8px 20px;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* 其他slide样式 */
        .slide-content {
            left: 50%;
            width: 90%;
            transform: translateX(-50%);
            top: 3%;
            bottom: auto;
            padding: 0 5% 20px;
            min-height: auto;
        }

        .slide-title {
            font-size: clamp(22px, 6vw, 28px);
            padding-top: 5px;
            margin-bottom: 8px;
        }

        .slide-desc {
            font-size: clamp(14px, 3.2vw, 16px);
            margin: 8px 0 12px;
            line-height: 1.5;
        }

        .slide-button {
            padding: 8px 20px;
            font-size: clamp(14px, 3.5vw, 16px);
            margin: 15px auto 10px;
            background: #ff6b0036 !important;
            border: 3px solid #ff6b00;
            color: #ff6b00;
            font-weight: 700;
        }

        .sw-indicator {
            width: 30px;
            height: 2px;
        }

        .sw-indicators {
            bottom: 15px;
        }

        .sw-nav-button {
            width: 40px;
            height: 40px;
        }

        .sw-nav-button i {
            font-size: 18px;
        }
    }

    /* 小屏幕手机特殊适配 */
    @media (max-width: 480px) {
        .anni-main-title {
            font-size: 1.3rem !important;
        }

        .anni-countdown-item {
            padding: 6px 3px !important;
        }

        .anni-countdown-value {
            font-size: 1rem;
        }
    }
</style>
<!--  弹窗样式  -->
<style>
    #newsletter-modal .coupon_info_box .coupon_info_title {
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;
    }

    #newsletter-modal .coupon_info_box .coupon_info_content {
        margin-top: 12px;
        font-size: 18px;
        color: #ffffff;
    }

    #newsletter-modal .coupon_info_box .coupon_info_discounted {
        padding: 10px 0;
        background: url({{static_path}}/assets/images/newsletter/coupon_info_bg.png) no-repeat center;
        min-height: 117px;
        margin-top: 33px;
        box-sizing: border-box;
    }

    #newsletter-modal .coupon_info_box .coupon_info_discounted .coupon_discount {
        color: #242424;
        font-size: 18px;
    }

    #newsletter-modal .coupon_info_box .coupon_info_discounted .coupon_price {
        margin-top: 8px;
        color: #ff0000;
        font-size: 30px;
        font-weight: bold;
        line-height: 1;
    }

    #newsletter-modal .coupon_info_box .coupon_info_discounted .coupon_date {
        margin-top: 8px;
        color: #242424;
        font-size: 14px;
    }

    #newsletter-modal .coupon_info_box .coupon_info_btn {
        display: inline-block;
        margin-top: 22px;
        width: 100%;
        line-height: 50px;
        max-width: 240px;
        text-align: center;
        border-radius: 5px;
        cursor: pointer;
        font-size: 24px;
    }
</style>
<script type="text/javascript">

    // 选择所有具有类名 'LoadMore' 的元素
    //const buttons = document.querySelectorAll('.LoadMore');

    // 为每个按钮添加点击事件监听器
    //buttons.forEach(function (button) {
    //    button.addEventListener('click', function () {
    //        if (button.previousElementSibling.classList.contains('more')) {
    //            button.previousElementSibling.classList.remove('more');
    //            button.textContent = "See Less";
    //        } else {
    //            button.previousElementSibling.classList.add('more');
    //            button.textContent = "Read More";

    //        }
    //    });
    //});

    function newsletter_popup() {
        var modal = document.querySelector('#newsletter-modal');
        var fademodal = document.querySelector('#newsletter-modal-fade');
        setTimeout(function () {
            modal.style.display = 'block';
            modal.classList.add('mfp-zoom-in');
            modal.classList.remove('mfp-hide');
            fademodal.classList.remove('mfp-hide');

            // 关闭按钮逻辑
            var closeButton = modal.querySelector('.mfp-close');
            if (closeButton || closeButtonclick) {
                closeButton.addEventListener('click', function () {
                    modal.style.display = 'none';
                    fademodal.style.display = 'none';
                });
            }
        }, 3000);
        var newslettercloseBtn = document.querySelector('.coupon_info_btn');
        newslettercloseBtn.addEventListener('click', function () {
            modal.style.display = 'none';
            fademodal.style.display = 'none';
        });

    }

</script>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        // 创建粒子效果
        function createParticles() {
            var particlesBg = document.querySelector('.anni-particles-bg');
            if (!particlesBg) return;

            var particleCount = 30;
            for (var i = 0; i < particleCount; i++) {
                var particle = document.createElement('div');
                particle.className = 'anni-particle';

                var size = Math.random() * 5 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 5 + 's';

                particlesBg.appendChild(particle);
            }
        }

        // 倒计时
        //function updateCountdown() {
        //    var countdownDate = new Date('2025-07-21T10:00:00').getTime();
        //    var now = new Date().getTime();
        //    var distance = countdownDate - now;

        //    var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        //    var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        //    var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        //    var seconds = Math.floor((distance % (1000 * 60)) / 1000);

        //    document.getElementById('anni-days').textContent = days.toString().padStart(2, '0');
        //    document.getElementById('anni-hours').textContent = hours.toString().padStart(2, '0');
        //    document.getElementById('anni-minutes').textContent = minutes.toString().padStart(2, '0');
        //    document.getElementById('anni-seconds').textContent = seconds.toString().padStart(2, '0');
        //}

        // 初始化粒子效果和倒计时
        createParticles();
        //updateCountdown();
        //setInterval(updateCountdown, 1000);

        class ManualSlider {
            constructor(container) {
                if (container != null) {
                    this.container = container;
                    this.slides = Array.from(container.querySelectorAll('.sw-slide'));
                    this.images = Array.from(container.querySelectorAll('img'));
                    this.images.forEach(img => img.dataset.pc = img.src);
                    this.indicatorsContainer = container.querySelector('.sw-indicators');
                    this.prevButton = container.querySelector('.sw-nav-button.prev');
                    this.nextButton = container.querySelector('.sw-nav-button.next');
                    this.currentIndex = 0;
                    this.isMobile = this.detectMobile();
                    this.touchStartX = 0;
                    this.swipeThreshold = 50;
                    this.resizeTimer = null;

                    this.initImageSources();
                    this.createIndicators();
                    this.setupEventListeners();
                    this.initAspectRatio();
                    this.setInitialHeight();
                    window.addEventListener('resize', this.handleResize.bind(this));
                }
            }

            setInitialHeight() {
                const firstImg = this.container.querySelector('.sw-slide.active img');
                if (firstImg && firstImg.complete) {
                    this.updateContainerRatio(firstImg);
                }
            }

            handleResize() {
                clearTimeout(this.resizeTimer);
                this.resizeTimer = setTimeout(() => {
                    this.setInitialHeight();
                }, 250);
            }

            detectMobile() {
                return window.matchMedia('(max-width: 768px)').matches;
            }

            initImageSources() {
                this.images.forEach(img => {
                    const targetSrc = this.isMobile ? img.dataset.mobile : img.dataset.pc;
                    if (img.src !== targetSrc) {
                        img.onload = () => this.updateAspectRatio();
                        img.src = targetSrc;
                    }
                });
            }

            initAspectRatio() {
                const activeImg = this.container.querySelector('.sw-slide.active img');
                if (activeImg && activeImg.complete) {
                    this.updateContainerRatio(activeImg);
                } else if (activeImg) {
                    activeImg.onload = () => this.updateContainerRatio(activeImg);
                }
            }

            updateContainerRatio(img) {
                const naturalWidth = img.naturalWidth;
                const naturalHeight = img.naturalHeight;
                if (naturalWidth > 0 && naturalHeight > 0) {
                    this.container.style.aspectRatio = `${naturalWidth}/${naturalHeight}`;
                }
            }

            createIndicators() {
                this.slides.forEach((_, index) => {
                    const indicator = document.createElement('div');
                    indicator.className = `sw-indicator ${index === 0 ? 'active' : ''}`;
                    indicator.addEventListener('click', () => this.goTo(index));
                    this.indicatorsContainer.appendChild(indicator);
                });
            }

            setupEventListeners() {
                const mediaQuery = window.matchMedia('(max-width: 768px)');
                mediaQuery.addEventListener('change', (e) => {
                    this.isMobile = e.matches;
                    this.initImageSources();
                    this.initAspectRatio();
                });

                window.addEventListener('resize', () => {
                    clearTimeout(this.resizeTimer);
                    this.resizeTimer = setTimeout(() => {
                        const wasMobile = this.isMobile;
                        this.isMobile = this.detectMobile();
                        if (wasMobile !== this.isMobile) {
                            this.initImageSources();
                            this.initAspectRatio();
                        }
                    }, 250);
                });

                // 添加导航按钮事件
                if (this.prevButton) {
                    this.prevButton.addEventListener('click', () => this.prev());
                }

                if (this.nextButton) {
                    this.nextButton.addEventListener('click', () => this.next());
                }

                this.container.addEventListener('touchstart', (e) => {
                    this.touchStartX = e.touches[0].clientX;
                }, {passive: true});

                this.container.addEventListener('touchend', (e) => {
                    const touchEndX = e.changedTouches[0].clientX;
                    const deltaX = touchEndX - this.touchStartX;
                    if (Math.abs(deltaX) > this.swipeThreshold) {
                        deltaX > 0 ? this.prev() : this.next();
                    }
                }, {passive: true});
            }

            goTo(index) {
                this.currentIndex = (index + this.slides.length) % this.slides.length;
                this.slides.forEach(slide => slide.classList.remove('active'));
                const activeSlide = this.slides[this.currentIndex];
                activeSlide.classList.add('active');

                const activeImg = activeSlide.querySelector('img');
                if (activeImg && activeImg.complete) {
                    this.updateContainerRatio(activeImg);
                } else if (activeImg) {
                    activeImg.onload = () => this.updateContainerRatio(activeImg);
                }

                this.updateIndicators();
            }

            updateIndicators() {
                this.indicatorsContainer.querySelectorAll('.sw-indicator').forEach((indicator, index) => {
                    indicator.classList.toggle('active', index === this.currentIndex);
                });
            }

            next() {
                this.goTo(this.currentIndex + 1);
            }

            prev() {
                this.goTo(this.currentIndex - 1);
            }
        }

        // 初始化手动轮播
        const slider = new ManualSlider(document.querySelector('.sw-container'));

        // 确保添加高度强制刷新
        setTimeout(() => {
            const container = document.querySelector('.sw-container');
            if (container) container.style.height = 'auto';
        }, 50);

        // 预加载优化
        window.addEventListener('load', () => {
            document.querySelectorAll('.sw-slide:not(.active) img').forEach(img => {
                img.setAttribute('loading', 'lazy');
            });
        });
    });
</script>

<!-- 导入弹窗组件 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<!-- 导入快速预览脚本 -->
<script src="/businessJs/Product/Index/quickView_t200.js"></script>

<script>
    // 设置静态资源路径全局变量，供quickView_t200.js使用
    window.staticPath = '{{ static_path }}';

    document.addEventListener('DOMContentLoaded', function () {
        document.addEventListener('click', function (e) {
            // 购物车按钮现在直接跳转到产品详情页面，不需要特殊处理

            // 处理收藏按钮点击事件
            const wishlistBtn = e.target.closest('.wishlist-btn .add-to-wishlist');
            if (wishlistBtn) {
                e.preventDefault();
                e.stopPropagation();

                // 直接从wishlist按钮获取产品ID和收藏状态
                const productId = wishlistBtn.getAttribute('data-product-id');
                const isFavorited = wishlistBtn.getAttribute('data-is-favorited') === 'true';

                if (!productId) {
                    if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                        customize_pop.warning('Unable to get product ID', null, null, {showIcon: false});
                    } else {
                        alert('Unable to get product ID');
                    }
                    return;
                }

                // 根据当前收藏状态决定操作
                const url = isFavorited ? '/Account/RemoveFromWishlistByProductId' : '/Account/AddToWishlist';
                const requestBody = {productId: parseInt(productId)};

                // 发送收藏请求
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 根据操作类型显示不同的成功消息
                            const message = isFavorited ? 'Removed from wishlist successfully' : 'Added to wishlist successfully';
                            if (typeof customize_pop !== 'undefined' && customize_pop.success) {
                                customize_pop.success(message, null, null, {showIcon: false});
                            } else {
                                alert(message);
                            }

                            // 更新按钮状态
                            const heartIcon = wishlistBtn.querySelector('i');
                            if (heartIcon) {
                                if (isFavorited) {
                                    heartIcon.className = 'icon an an-heart-o'; // 改为空心心形
                                    wishlistBtn.setAttribute('data-is-favorited', 'false');
                                } else {
                                    heartIcon.className = 'icon an an-heart'; // 改为实心心形
                                    wishlistBtn.setAttribute('data-is-favorited', 'true');
                                }
                            }
                        } else {
                            if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                                customize_pop.warning(data.message || 'Operation failed', null, null, {showIcon: false});
                            } else {
                                alert(data.message || 'Operation failed');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('收藏操作时出错:', error);
                        if (typeof customize_pop !== 'undefined' && customize_pop.error) {
                            customize_pop.error('Network error occurred', null, null, {showIcon: false});
                        } else {
                            alert('Network error occurred');
                        }
                    });
            }
        });
    });
</script>