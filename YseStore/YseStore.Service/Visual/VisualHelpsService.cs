using Aop.Api.Domain;
using Entitys;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.Order;
using YseStore.IService.Sales;
using YseStore.IService.Set;
using YseStore.IService.Shopping;
using YseStore.IService.Visual;
using YseStore.Model.Entities.Blog;
using YseStore.Service.Order;

namespace YseStore.Service.Visual
{
    public class VisualHelpsService: IVisualHelpsService
    {
        private readonly ILogger<OrderService> _logger;
        private readonly ISqlSugarClient db;

        public VisualHelpsService(ILogger<OrderService> logger, ISqlSugarClient db)
        {
            _logger = logger;
            this.db = db;
        }


        /// <summary>
        /// 设置宽度容器
        /// </summary>
        /// <param name="ContainerWidth"></param>
        /// <returns></returns>
        public  string SetContainWidth(string ContainerWidth="")
        {
           
            var className = string.Empty;

            // 处理不同宽度类型
            switch (ContainerWidth?.ToLower())
            {
                case "standard":
                    className = "container_screen";
                    break;
                case "full":
                    className = "container_full";
                    break;
                default:
                    // 数值类型验证（支持整型和字符串数值）
                    if (int.TryParse(ContainerWidth, out int width))
                    {
                        className = $"container_width_{width}";
                    }
                    break;
            }

            return className;
        }


        /// <summary>
        /// 计算图片尺寸比例--计算比率
        /// </summary>
        /// <param name="replaceAry"></param>
        /// <param name="type"></param>
        /// <param name="productsPicScale"></param>
        /// <returns></returns>
        public string ComputeRatio(Dictionary<string, object> replaceAry = null, string type = "default", string productsPicScale = null)
        {
            replaceAry ??= new Dictionary<string, object>();
            string style = "";

            if (type == "default")
            {  // 默认类型
                style = ComputeDefaultRatio(replaceAry);
            }
            else if (type == "products" || type == "goods")
            {  // 产品类型
                bool isCss = type != "goods";
                style = ComputeProRatio(replaceAry, productsPicScale, isCss);
            }
            else if (type == "fixed")
            {  // 固定类型
                if (replaceAry.ContainsKey("height") && replaceAry["height"] != null)
                {
                    style = $"padding-top: {replaceAry["height"]}px";
                }
            }

            return style;
        }

        public  string ComputeDefaultRatio(Dictionary<string, object> replaceAry = null)
        {
            replaceAry ??= new Dictionary<string, object>();
            float width = replaceAry.ContainsKey("width") && replaceAry["width"] != null ? Convert.ToSingle(replaceAry["width"]) : 0;
            float height = replaceAry.ContainsKey("height") && replaceAry["height"] != null ? Convert.ToSingle(replaceAry["height"]) : 0;

            string style = "";
            if (width > 0 && height > 0)
            {
                float proportion = (height / width) * 100;
                proportion = (float)Math.Round(proportion, 2);
                style = $"padding-top:{proportion}%";
            }

            return style;
        }
        /// <summary>
        /// 计算宽度
        /// </summary>
        /// <param name="replaceAry"></param>
        /// <param name="picScale"></param>
        /// <param name="scrollView"></param>
        /// <returns></returns>
        public string ComputeWidth(Dictionary<string, object> replaceAry = null, string picScale = "", int scrollView = 0)
        {
            if (string.IsNullOrEmpty(picScale) || scrollView != 0) return "";
            replaceAry ??= new Dictionary<string, object>();

            float width = replaceAry.ContainsKey("width") && replaceAry["width"] != null ? Convert.ToSingle(replaceAry["width"]) : 0;
            string style = "max-width:100%;margin:auto;";

            if (width > 0)
            {
                style += $"width:{width}px;";
            }
            return style;
        }

        public  string ComputeProRatio(Dictionary<string, object> replaceAry = null, string productsPicScale = null, bool isCss = true)
        {
            replaceAry ??= new Dictionary<string, object>();
            float width = replaceAry.ContainsKey("width") && replaceAry["width"] != null ? Convert.ToSingle(replaceAry["width"]) : 1;
            float height = replaceAry.ContainsKey("height") && replaceAry["height"] != null ? Convert.ToSingle(replaceAry["height"]) : 1;

            string proPicScale = replaceAry.ContainsKey("ProductsPicScale") && replaceAry["ProductsPicScale"] != null ? replaceAry["ProductsPicScale"].ToString() : "";

            // 模拟PHP的str::json_data函数 - 这里假设返回的是Dictionary<string, object>
            Dictionary<string, object> sizeAry = !string.IsNullOrEmpty(productsPicScale) ?
                Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(productsPicScale) :
                new Dictionary<string, object>();

            if (proPicScale == "adapt")
            {
                if (sizeAry.ContainsKey("width") && sizeAry["width"] != null &&
                    sizeAry.ContainsKey("height") && sizeAry["height"] != null)
                {
                    width = Convert.ToSingle(sizeAry["width"]);
                    height = Convert.ToSingle(sizeAry["height"]);
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(proPicScale))
                {
                    string[] proPicScaleAry = proPicScale.Split('_');
                    if (proPicScaleAry.Length >= 2)
                    {
                        width = float.Parse(proPicScaleAry[0]);
                        height = float.Parse(proPicScaleAry[1]);
                    }
                }
            }

            string style = "";
            if (width > 0 && height > 0)
            {
                float proportion = (height / width) * 100;
                proportion = (float)Math.Round(proportion, 2);
                if (isCss)
                {
                    style = $"padding-top:{proportion}%";
                }
                else
                {
                    style = $"{proportion}%";
                }
            }

            return style;
        }

        // 获取平铺方式
        public  string ComputeFilling(Dictionary<string, object> fillingAry = null)
        {
            fillingAry ??= new Dictionary<string, object>();
            string fillingMethod = fillingAry.ContainsKey("FillingMethod") ? fillingAry["FillingMethod"]?.ToString() : "";
            string picDisplayArea = fillingAry.ContainsKey("PicDisplayArea") ? fillingAry["PicDisplayArea"]?.ToString() : "";

            if (fillingMethod == "tiled")
            {
                var displayAry = new Dictionary<string, string>
            {
                { "top", "top center" },
                { "center", "center center" },
                { "bottom", "bottom center" }
            };

                string position = displayAry.ContainsKey(picDisplayArea) ? displayAry[picDisplayArea] : "center center";
                return $"top:0;left:0;right:0;bottom:0;margin:auto;width:100%;height:100%;object-fit:cover;object-position:{position}";
            }
            else if (fillingMethod == "ratio")
            {
                return "top:0;left:0;right:0;bottom:0;margin:auto;";
            }
            else
            {
                if (string.IsNullOrEmpty(picDisplayArea))
                {
                    picDisplayArea = "center";
                }

                var displayAry = new Dictionary<string, string>
            {
                { "left", "top:0;left:0;" },
                { "center", "top:0;left:0;right:0;bottom:0;margin:auto;" }
            };

                return displayAry.ContainsKey(picDisplayArea) ? displayAry[picDisplayArea] : "top:0;left:0;right:0;bottom:0;margin:auto;";
            }
        }





        /// <summary>
        /// 店铺装修--获取博客内容
        /// </summary>
        /// <param name="valueJson"></param>
        /// <param name="normalLimit"></param>
        /// <returns></returns>
        public List<BlogNew> GetBlogList(string valueJson = "", int normalLimit = 3)
        {
            // 先进行HTML解码，将 &quot; 转换为 "
            var decodedJson = WebUtility.HtmlDecode(valueJson);
            var value = JsonConvert.DeserializeObject<BlogParams>(decodedJson);
            //var limit = normalLimit;
            List<BlogNew> result = new List<BlogNew>();

            // 手动模式
            if (value?.Method == "manual")
            {
                var ids = value.Id?.Split(',').Select(int.Parse).ToList() ?? new List<int>();
                
                var query = db.Queryable<BlogNew>().Where(b => ids.Contains(b.AId));
                    
                result = query
                    .OrderBy(x => x.AId,OrderByType.Desc)
                    //.Take(limit)
                    .ToList();
            }
            // 智能模式
            else if (value?.Method == "intelligent")
            {
                var query = db.Queryable<BlogNew>(); 
                // 构建查询条件
                var exp = Expressionable.Create<BlogNew>();

                // 分类筛选
                if (value.ScopeType == "specify_category" && value.CateId?.Any() == true)
                {
                    exp = exp.And(b => value.CateId.Contains(b.CateId.ToString()));
                }

                // 标签筛选
                if (value.TagsId?.Any() == true)
                {
                    foreach (var tag in value.TagsId)
                    {
                        exp = exp.And(b => SqlFunc.Contains(b.Tag, $"|{tag}|"));
                    }
                }

                // 排序条件
                if (value.ShowType == "hot")
                {
                    result = query
                        .Where(exp.ToExpression())
                        .OrderBy(x => x.ViewCount, OrderByType.Desc)
                        .Take(value.Limit)
                        .ToList();
                }
                else
                {
                    result = query
                        .Where(exp.ToExpression())
                        .OrderBy(x => x.AId, OrderByType.Desc)
                        .Take(value.Limit)
                        .ToList();
                }
                
            }

          

            return result;
        }


        public class BlogParams
        {
            public string Method { get; set; }
            public string Id { get; set; }
            public string ShowType { get; set; }
            public string ScopeType { get; set; }
            public List<string> TagsId { get; set; }
            public List<string> CateId { get; set; }
            public int Limit { get; set; } = 3;
        }













    }
}
